<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Facebook-style Reactions Demo')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-semibold mb-4">Facebook-style Reaction System</h3>
                    
                    <div class="space-y-6">
                        <!-- Demo Post 1 -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                            <div class="flex items-center space-x-3 mb-4">
                                <img class="h-10 w-10 rounded-full" 
                                     src="https://ui-avatars.com/api/?name=Demo+User&color=3B82F6&background=DBEAFE" 
                                     alt="Demo User">
                                <div>
                                    <h4 class="font-semibold text-gray-900">Demo User</h4>
                                    <p class="text-sm text-gray-500">2 hours ago</p>
                                </div>
                            </div>
                            
                            <p class="text-gray-800 mb-4">
                                This is a demo post to showcase the Facebook-style reaction system! 
                                Hover over the Like button to see all available reactions, or click to quickly like.
                                On mobile, long-press the Like button to see reaction options.
                            </p>
                            
                            <div class="border-t border-gray-200 pt-4">
                                <div class="flex items-center space-x-6">
                                    <!-- Demo reaction component -->
                                    <div class="reaction-wrapper relative inline-block">
                                        <button class="reaction-btn flex items-center space-x-2 text-gray-500 transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
                                                data-target-id="demo-1"
                                                data-target-type="post"
                                                data-current-reaction="">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-2-2v9a2 2 0 002 2h2m0-10V9a2 2 0 002 2h2" />
                                            </svg>
                                            <span class="text-sm font-medium">Like</span>
                                        </button>
                                        <span class="reaction-count text-xs text-gray-500 ml-1" style="display: none;">0</span>
                                    </div>
                                    
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                        <span class="text-sm">Comment</span>
                                    </button>
                                    
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                        <span class="text-sm">Share</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Demo Post 2 -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                            <div class="flex items-center space-x-3 mb-4">
                                <img class="h-10 w-10 rounded-full" 
                                     src="https://ui-avatars.com/api/?name=Another+User&color=10B981&background=D1FAE5" 
                                     alt="Another User">
                                <div>
                                    <h4 class="font-semibold text-gray-900">Another User</h4>
                                    <p class="text-sm text-gray-500">5 hours ago</p>
                                </div>
                            </div>
                            
                            <p class="text-gray-800 mb-4">
                                Another demo post! Try different reactions and see how they work. 
                                The system supports Like, Love, Haha, Wow, Sad, and Angry reactions just like Facebook.
                            </p>
                            
                            <div class="border-t border-gray-200 pt-4">
                                <div class="flex items-center space-x-6">
                                    <!-- Demo reaction component -->
                                    <div class="reaction-wrapper relative inline-block">
                                        <button class="reaction-btn flex items-center space-x-2 text-gray-500 transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
                                                data-target-id="demo-2"
                                                data-target-type="post"
                                                data-current-reaction="">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.60L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-2-2v9a2 2 0 002 2h2m0-10V9a2 2 0 002 2h2" />
                                            </svg>
                                            <span class="text-sm font-medium">Like</span>
                                        </button>
                                        <span class="reaction-count text-xs text-gray-500 ml-1" style="display: none;">0</span>
                                    </div>
                                    
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                        </svg>
                                        <span class="text-sm">Comment</span>
                                    </button>
                                    
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-blue-600 transition-colors">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                        </svg>
                                        <span class="text-sm">Share</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Instructions -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <h4 class="font-semibold text-blue-900 mb-2">How to use:</h4>
                            <ul class="text-blue-800 space-y-1">
                                <li><strong>Desktop:</strong> Hover over the Like button to see all reaction options</li>
                                <li><strong>Mobile:</strong> Long-press the Like button to see reaction options</li>
                                <li><strong>Quick Like:</strong> Click/tap the Like button to quickly toggle a like</li>
                                <li><strong>Change Reaction:</strong> Select a different reaction from the popup to change your reaction</li>
                                <li><strong>Remove Reaction:</strong> Click your current reaction to remove it</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\demo\reactions.blade.php ENDPATH**/ ?>