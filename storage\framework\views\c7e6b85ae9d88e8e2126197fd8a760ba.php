<!-- <PERSON> (Facebook-style) -->
<div id="image-modal" class="fixed inset-0 z-50 hidden bg-black bg-opacity-90 flex items-center justify-center">
    <!-- Close Button -->
    <button onclick="closeImageModal()" class="absolute top-4 right-4 z-60 text-white hover:text-gray-300 transition-colors">
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
    </button>

    <!-- Previous Button -->
    <button id="prev-btn" onclick="previousImage()" class="absolute left-4 top-1/2 transform -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
    </button>

    <!-- Next Button -->
    <button id="next-btn" onclick="nextImage()" class="absolute right-4 top-1/2 transform -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors p-2 rounded-full bg-black bg-opacity-50 hover:bg-opacity-70">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
    </button>

    <!-- Main Image Container -->
    <div class="flex items-center justify-center w-full h-full p-4">
        <div class="relative max-w-full max-h-full flex items-center justify-center">
            <!-- Loading Spinner -->
            <div id="image-loading" class="absolute inset-0 flex items-center justify-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            </div>
            
            <!-- Main Image -->
            <img id="modal-image" 
                 src="" 
                 alt="Full size image" 
                 class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                 style="max-height: 90vh; max-width: 90vw;"
                 onload="hideImageLoading()"
                 onerror="hideImageLoading()">
        </div>
    </div>

    <!-- Image Counter -->
    <div id="image-counter" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white bg-black bg-opacity-50 px-3 py-1 rounded-full text-sm">
        <span id="current-image">1</span> / <span id="total-images">1</span>
    </div>

    <!-- Image Info (Optional) -->
    <div id="image-info" class="absolute bottom-16 left-1/2 transform -translate-x-1/2 text-white text-center max-w-md">
        <p id="image-description" class="text-sm opacity-80"></p>
    </div>
</div>

<script>
// Prevent redeclaration if already loaded
if (typeof window.imageModalLoaded === 'undefined') {
    window.imageModalLoaded = true;

    var currentImages = [];
    var currentImageIndex = 0;

    // Open image modal
    function openImageModal(images, startIndex = 0) {
        currentImages = images.map(image => {
            // Handle both full URLs and storage paths
            if (image.startsWith('http')) {
                return image;
            } else if (image.startsWith('storage/')) {
                return '/' + image;
            } else {
                return '/storage/' + image;
            }
        });
        currentImageIndex = startIndex;
    
    const modal = document.getElementById('image-modal');
    const modalImage = document.getElementById('modal-image');
    const currentImageSpan = document.getElementById('current-image');
    const totalImagesSpan = document.getElementById('total-images');
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const imageCounter = document.getElementById('image-counter');
    
    // Show modal
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
    
    // Update image counter
    currentImageSpan.textContent = currentImageIndex + 1;
    totalImagesSpan.textContent = currentImages.length;
    
    // Show/hide navigation buttons and counter
    if (currentImages.length > 1) {
        prevBtn.style.display = 'block';
        nextBtn.style.display = 'block';
        imageCounter.style.display = 'block';
    } else {
        prevBtn.style.display = 'none';
        nextBtn.style.display = 'none';
        imageCounter.style.display = 'none';
    }
    
    // Load the image
    loadImage(currentImageIndex);
}

// Close image modal
function closeImageModal() {
    const modal = document.getElementById('image-modal');
    modal.classList.add('hidden');
    document.body.style.overflow = 'auto'; // Restore scrolling
    currentImages = [];
    currentImageIndex = 0;
}

// Load image at specific index
function loadImage(index) {
    const modalImage = document.getElementById('modal-image');
    const currentImageSpan = document.getElementById('current-image');
    const loadingSpinner = document.getElementById('image-loading');
    
    // Show loading spinner
    loadingSpinner.style.display = 'flex';
    modalImage.style.opacity = '0';
    
    // Update counter
    currentImageSpan.textContent = index + 1;
    
    // Load new image
    modalImage.src = currentImages[index];
    currentImageIndex = index;
}

// Hide loading spinner
function hideImageLoading() {
    const loadingSpinner = document.getElementById('image-loading');
    const modalImage = document.getElementById('modal-image');
    
    loadingSpinner.style.display = 'none';
    modalImage.style.opacity = '1';
}

// Navigate to previous image
function previousImage() {
    if (currentImages.length > 1) {
        const newIndex = currentImageIndex > 0 ? currentImageIndex - 1 : currentImages.length - 1;
        loadImage(newIndex);
    }
}

// Navigate to next image
function nextImage() {
    if (currentImages.length > 1) {
        const newIndex = currentImageIndex < currentImages.length - 1 ? currentImageIndex + 1 : 0;
        loadImage(newIndex);
    }
}

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    const modal = document.getElementById('image-modal');
    if (!modal.classList.contains('hidden')) {
        switch(e.key) {
            case 'Escape':
                closeImageModal();
                break;
            case 'ArrowLeft':
                previousImage();
                break;
            case 'ArrowRight':
                nextImage();
                break;
        }
    }
});

// Close modal when clicking on background
document.getElementById('image-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});

// Prevent image click from closing modal
document.getElementById('modal-image').addEventListener('click', function(e) {
    e.stopPropagation();
});

// Touch/swipe support for mobile
let touchStartX = 0;
let touchEndX = 0;

document.getElementById('image-modal').addEventListener('touchstart', function(e) {
    touchStartX = e.changedTouches[0].screenX;
});

document.getElementById('image-modal').addEventListener('touchend', function(e) {
    touchEndX = e.changedTouches[0].screenX;
    handleSwipe();
});

function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartX - touchEndX;
    
    if (Math.abs(diff) > swipeThreshold) {
        if (diff > 0) {
            // Swiped left - next image
            nextImage();
        } else {
            // Swiped right - previous image
            previousImage();
        }
    }
}

// Preload adjacent images for better performance
function preloadAdjacentImages() {
    if (currentImages.length > 1) {
        const prevIndex = currentImageIndex > 0 ? currentImageIndex - 1 : currentImages.length - 1;
        const nextIndex = currentImageIndex < currentImages.length - 1 ? currentImageIndex + 1 : 0;
        
        // Preload previous image
        const prevImg = new Image();
        prevImg.src = currentImages[prevIndex];
        
        // Preload next image
        const nextImg = new Image();
        nextImg.src = currentImages[nextIndex];
    }
}

// Call preload when image loads
document.getElementById('modal-image').addEventListener('load', preloadAdjacentImages);

} // End of imageModalLoaded check
</script>

<style>
#image-modal {
    backdrop-filter: blur(2px);
}

#modal-image {
    transition: opacity 0.3s ease;
}

/* Smooth transitions for navigation buttons */
#prev-btn, #next-btn {
    transition: all 0.2s ease;
}

#prev-btn:hover, #next-btn:hover {
    transform: translateY(-50%) scale(1.1);
}

/* Loading animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #prev-btn, #next-btn {
        padding: 8px;
    }
    
    #prev-btn svg, #next-btn svg {
        width: 20px;
        height: 20px;
    }
    
    #image-counter {
        bottom: 20px;
        font-size: 12px;
    }
}
</style>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\components\image-modal.blade.php ENDPATH**/ ?>