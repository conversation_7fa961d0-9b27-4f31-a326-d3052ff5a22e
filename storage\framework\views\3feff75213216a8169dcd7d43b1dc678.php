<!-- <PERSON>u <PERSON> -->
<div class="px-4 py-4 bg-custom-green">
    <h2 class="text-lg font-bold text-custom-darkest px-3 flex items-center">
        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
        Menu
    </h2>
</div>

<!-- Navigation Menu -->
<nav class="flex-1 px-4 py-4 space-y-2">
    <!-- Profile -->
    <a href="<?php echo e(route('profile.show')); ?>" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('profile*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest'); ?>">
        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        Profile
    </a>

    <!-- Campuses -->
    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-custom-darkest hover:bg-custom-second-darkest">
        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        Campuses
    </a>

    <!-- Organizations & Pages -->
    <a href="<?php echo e(route('organizations.index')); ?>" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('organizations*') || request()->routeIs('pages*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest'); ?>">
        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        Organizations & Pages
    </a>

    <!-- Student Groups -->
    <a href="<?php echo e(route('groups.index')); ?>" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('groups*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest'); ?>">
        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        Student Groups
    </a>

    <!-- Demo (New Features) -->
    <a href="<?php echo e(route('demo.index')); ?>" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('demo*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest'); ?> border-2 border-dashed border-blue-300">
        <svg class="w-5 h-5 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
        <span class="text-blue-600 font-semibold">🎉 New Features Demo</span>
    </a>

    <!-- Find Scholarships -->
    <a href="<?php echo e(route('scholarships.index')); ?>" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('scholarships*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-darkest hover:bg-custom-second-darkest'); ?>">
        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Find Scholarships
    </a>

    <!-- Admin Dashboard -->
    <?php if(auth()->user()->hasManagementAccess()): ?>
        <a href="<?php echo e(route('admin.dashboard')); ?>" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-custom-darkest hover:bg-custom-second-darkest">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Admin Dashboard
        </a>
    <?php endif; ?>

    <!-- Student Records -->
    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-custom-darkest hover:bg-custom-second-darkest">
        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        Student Records
    </a>

    <!-- Validate Students -->
    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-custom-darkest hover:bg-custom-second-darkest">
        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Validate Students
    </a>
</nav>

    <!-- Logout -->
    <form method="POST" action="<?php echo e(route('logout')); ?>">
        <?php echo csrf_field(); ?>
        <button type="submit" class="flex items-center w-full px-3 py-2 text-sm font-medium text-custom-darkest rounded-lg hover:bg-custom-second-darkest">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Logout
        </button>
    </form>
</nav>



<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\layouts\unilink-sidebar-content.blade.php ENDPATH**/ ?>