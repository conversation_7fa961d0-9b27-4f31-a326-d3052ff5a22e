<div>
    <?php if(auth()->guard()->check()): ?>
        <?php if($userMembership): ?>
            <?php if($userMembership->pivot->status === 'pending'): ?>
                <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                    Pending Approval
                </span>
            <?php elseif($userMembership->pivot->status === 'active'): ?>
                <div class="flex items-center space-x-2">
                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                        <?php echo e(ucfirst($userMembership->pivot->role)); ?>

                    </span>
                    <?php if($group->created_by !== auth()->id()): ?>
                        <button
                            wire:click="leaveGroup"
                            wire:loading.attr="disabled"
                            wire:confirm="Are you sure you want to leave this group?"
                            class="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium hover:bg-red-700 disabled:opacity-50"
                            :disabled="$isLoading">
                            <span wire:loading.remove wire:target="leaveGroup">Leave</span>
                            <span wire:loading wire:target="leaveGroup">Leaving...</span>
                        </button>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <button
                wire:click="joinGroup"
                wire:loading.attr="disabled"
                class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50"
                :disabled="$isLoading">
                <span wire:loading.remove wire:target="joinGroup">
                    <?php echo e($group->visibility === 'private' ? 'Request to Join' : 'Join Group'); ?>

                </span>
                <span wire:loading wire:target="joinGroup">
                    <?php echo e($group->visibility === 'private' ? 'Requesting...' : 'Joining...'); ?>

                </span>
            </button>
        <?php endif; ?>
    <?php else: ?>
        <a href="<?php echo e(route('login')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
            Login to Join
        </a>
    <?php endif; ?>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\livewire\group-membership.blade.php ENDPATH**/ ?>